<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">视频播放</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <div class="row">
            <!-- 视频播放区域 -->
            <div class="col-12">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- Video.js 播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            width="270"
                            height="380"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl != null ? video.thumbnailUrl : ''}"
                            playsinline>
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            您的浏览器不支持HTML5视频播放。
                        </video>
                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h3 mb-3" th:text="${video.title}">视频标题</h1>
                     <div class="video-stats">
                            <time class="video-date" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                     </div>
                </div>
            </div>


        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 导航栏修复JS -->
    <script src="/js/navbar-fix.js"></script>
    <!-- 自定义JS -->
    <script src="/js/main.js"></script>


    <script th:inline="javascript">
        // 初始化原生HTML5视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;

            // 设置视频源
            if (videoUrl) {
                videoElement.src = videoUrl;
            }

            // 设置视频属性
            videoElement.style.width = '270px';
            videoElement.style.height = '380px';
            videoElement.style.objectFit = 'cover';

            // 如果没有poster，尝试生成首帧
            if (!videoElement.poster || videoElement.poster === '') {
                generateVideoPoster(videoElement);
            }

            // 确保视频在移动端正确显示
            videoElement.setAttribute('playsinline', '');
            videoElement.setAttribute('webkit-playsinline', '');

            // 设置preload为metadata以获取首帧
            videoElement.preload = 'metadata';

            // 原生HTML5视频事件监听
            console.log('原生HTML5视频播放器已准备就绪');

            // 错误处理
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);
                let errorMessage = '视频加载失败，请检查网络连接。';

                if (videoElement.error) {
                    console.error('错误代码:', videoElement.error.code);
                    console.error('错误信息:', videoElement.error.message);

                    switch (videoElement.error.code) {
                        case 1:
                            errorMessage = '视频加载被中止。';
                            break;
                        case 2:
                            errorMessage = '网络错误，无法加载视频。';
                            break;
                        case 3:
                            errorMessage = '视频解码失败或格式不支持。';
                            break;
                        case 4:
                            errorMessage = '视频不存在或无法访问。';
                            break;
                    }
                }

                // 移除可能存在的旧错误信息
                const oldError = videoElement.parentNode.querySelector('.alert');
                if (oldError) {
                    oldError.remove();
                }

                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}`;
                videoElement.parentNode.appendChild(errorDiv);

                // 尝试重新加载视频
                setTimeout(() => {
                    videoElement.src = videoUrl;
                    videoElement.load();
                }, 3000);
            });

            // 播放开始事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
            });

            // 视频暂停事件
            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            // 视频结束事件
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            // 视频加载完成事件
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
            });




        });
        // 生成视频首帧作为poster
        function generateVideoPoster(videoElement) {
            // 方法1: 尝试使用视频本身生成首帧
            const generateFromVideo = () => {
                try {
                    // 等待视频元数据加载
                    if (videoElement.readyState >= 1) {
                        // 创建canvas来捕获视频帧
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        canvas.width = 270;
                        canvas.height = 380;

                        // 绘制视频帧到canvas
                        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                        // 将canvas转换为图片URL
                        const posterUrl = canvas.toDataURL('image/jpeg', 0.8);

                        // 设置为视频的poster
                        videoElement.poster = posterUrl;

                        console.log('视频首帧已生成并设置为poster');
                        return true;
                    }
                } catch (error) {
                    console.warn('无法从主视频生成首帧:', error);
                }
                return false;
            };

            // 方法2: 使用临时video元素
            const generateFromTempVideo = () => {
                const tempVideo = document.createElement('video');
                tempVideo.src = videoElement.src;
                tempVideo.muted = true;
                tempVideo.style.display = 'none';
                tempVideo.preload = 'metadata';
                document.body.appendChild(tempVideo);

                const cleanup = () => {
                    if (tempVideo.parentNode) {
                        document.body.removeChild(tempVideo);
                    }
                };

                tempVideo.addEventListener('loadeddata', function() {
                    // 设置到第1秒获取首帧
                    tempVideo.currentTime = 1;
                });

                tempVideo.addEventListener('seeked', function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        canvas.width = 270;
                        canvas.height = 380;

                        ctx.drawImage(tempVideo, 0, 0, canvas.width, canvas.height);
                        const posterUrl = canvas.toDataURL('image/jpeg', 0.8);

                        videoElement.poster = posterUrl;
                        console.log('通过临时视频生成首帧成功');
                    } catch (error) {
                        console.warn('无法生成视频首帧:', error);
                    } finally {
                        cleanup();
                    }
                });

                tempVideo.addEventListener('error', function() {
                    console.warn('临时视频加载失败');
                    cleanup();
                });

                // 设置超时清理
                setTimeout(cleanup, 10000);

                tempVideo.load();
            };

            // 首先尝试从主视频生成
            videoElement.addEventListener('loadeddata', function() {
                if (!generateFromVideo()) {
                    // 如果失败，尝试使用临时视频
                    generateFromTempVideo();
                }
            });

            // 如果视频已经加载，立即尝试生成
            if (videoElement.readyState >= 1) {
                if (!generateFromVideo()) {
                    generateFromTempVideo();
                }
            }
        }












    </script>
</body>
</html>

