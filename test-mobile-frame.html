<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端视频首帧测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 10px;
            min-height: 100vh;
        }
        
        .mobile-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
        }
        
        .video-section {
            padding: 20px;
            text-align: center;
        }
        
        .video-container {
            width: 270px;
            height: 380px;
            margin: 0 auto 20px;
            border: 2px solid #ffffff;
            border-radius: 12px;
            overflow: hidden;
            background: #000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        /* 视频样式 - 与项目保持一致 */
        video {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: cover;
            background-position: center;
        }
        
        /* 移动端poster优化 */
        #mobile-video[poster] {
            background-size: cover !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
        }
        
        /* 移动端播放按钮样式 */
        video::-webkit-media-controls-play-button {
            background-color: rgba(255, 255, 255, 0.9) !important;
            border-radius: 50% !important;
            width: 60px !important;
            height: 60px !important;
            margin: auto !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            z-index: 2147483647 !important;
            position: relative !important;
            pointer-events: auto !important;
        }
        
        video::-webkit-media-controls-start-playback-button {
            background-color: rgba(255, 255, 255, 0.9) !important;
            border-radius: 50% !important;
            width: 60px !important;
            height: 60px !important;
            margin: auto !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            z-index: 2147483647 !important;
            position: relative !important;
            pointer-events: auto !important;
        }
        
        /* 控制栏样式 */
        video::-webkit-media-controls-panel {
            z-index: 2147483647 !important;
            position: relative !important;
            pointer-events: auto !important;
            background: rgba(0, 0, 0, 0.7) !important;
            border-radius: 8px !important;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .control-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 25px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            min-width: 80px;
            touch-action: manipulation;
        }
        
        .control-btn:active {
            background: #0056b3;
            transform: scale(0.95);
        }
        
        .status {
            background: #e3f2fd;
            padding: 15px;
            margin: 15px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            font-size: 12px;
        }
        
        .info-item strong {
            display: block;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        /* 移动端优化 */
        @media (max-width: 320px) {
            .video-container {
                width: 240px;
                height: 320px;
            }
            
            video {
                width: 240px !important;
                height: 320px !important;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <div class="header">
            📱 移动端视频首帧测试
        </div>
        
        <div class="video-section">
            <div class="video-container">
                <video id="mobile-video" controls preload="metadata" playsinline webkit-playsinline>
                    <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/sample-video.mp4" type="video/mp4">
                    您的浏览器不支持HTML5视频播放。
                </video>
            </div>
            
            <div class="controls">
                <button class="control-btn" onclick="generateFrame()">生成首帧</button>
                <button class="control-btn" onclick="clearFrame()">清除首帧</button>
                <button class="control-btn" onclick="playTest()">播放测试</button>
            </div>
        </div>
        
        <div class="info-grid">
            <div class="info-item">
                <strong>首帧状态</strong>
                <span id="frame-status">未生成</span>
            </div>
            <div class="info-item">
                <strong>视频状态</strong>
                <span id="video-status">加载中</span>
            </div>
            <div class="info-item">
                <strong>播放状态</strong>
                <span id="play-status">暂停</span>
            </div>
            <div class="info-item">
                <strong>控制栏</strong>
                <span id="controls-status">正常</span>
            </div>
        </div>
        
        <div class="status" id="main-status">
            正在初始化移动端视频播放器...
        </div>
    </div>

    <script>
        const video = document.getElementById('mobile-video');
        const mainStatus = document.getElementById('main-status');
        const frameStatus = document.getElementById('frame-status');
        const videoStatus = document.getElementById('video-status');
        const playStatus = document.getElementById('play-status');
        const controlsStatus = document.getElementById('controls-status');
        
        function updateMainStatus(message, color = '#1976d2') {
            mainStatus.innerHTML = message;
            mainStatus.style.color = color;
        }
        
        function updateFrameStatus(status) {
            frameStatus.textContent = status;
        }
        
        function updateVideoStatus(status) {
            videoStatus.textContent = status;
        }
        
        function updatePlayStatus(status) {
            playStatus.textContent = status;
        }
        
        // 生成视频首帧
        function generateVideoPoster(videoElement) {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = 270;
                canvas.height = 380;
                
                const captureFrame = function() {
                    try {
                        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
                        const posterDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                        videoElement.poster = posterDataUrl;
                        
                        updateMainStatus('✅ 移动端视频首帧生成成功！', '#4caf50');
                        updateFrameStatus('已生成');
                        console.log('移动端视频首帧生成成功');
                    } catch (error) {
                        updateMainStatus('❌ 无法生成视频首帧: ' + error.message, '#f44336');
                        updateFrameStatus('生成失败');
                        console.warn('无法生成视频首帧:', error);
                    }
                };

                if (videoElement.readyState >= 2) {
                    captureFrame();
                } else {
                    videoElement.addEventListener('canplay', captureFrame, { once: true });
                }
                
            } catch (error) {
                updateMainStatus('❌ 生成首帧失败: ' + error.message, '#f44336');
                updateFrameStatus('生成失败');
                console.warn('生成视频首帧失败:', error);
            }
        }
        
        function generateFrame() {
            updateMainStatus('🔄 正在生成移动端视频首帧...', '#ff9800');
            generateVideoPoster(video);
        }
        
        function clearFrame() {
            video.poster = '';
            updateMainStatus('🗑️ 已清除视频首帧', '#ff9800');
            updateFrameStatus('已清除');
        }
        
        function playTest() {
            if (video.paused) {
                video.play().then(() => {
                    updateMainStatus('▶️ 移动端播放测试成功', '#4caf50');
                    updatePlayStatus('播放中');
                }).catch(error => {
                    updateMainStatus('❌ 播放失败: ' + error.message, '#f44336');
                });
            } else {
                video.pause();
                updateMainStatus('⏸️ 视频已暂停', '#ff9800');
                updatePlayStatus('暂停');
            }
        }
        
        // 事件监听
        video.addEventListener('loadeddata', function() {
            updateMainStatus('📱 移动端视频加载完成，准备生成首帧...', '#4caf50');
            updateVideoStatus('已加载');
            // 自动生成首帧
            setTimeout(() => {
                generateVideoPoster(video);
            }, 500);
        });
        
        video.addEventListener('play', function() {
            updateMainStatus('▶️ 移动端播放中 - 首帧功能正常', '#ff9800');
            updatePlayStatus('播放中');
        });
        
        video.addEventListener('pause', function() {
            updateMainStatus('⏸️ 移动端已暂停 - 首帧功能正常', '#ff9800');
            updatePlayStatus('暂停');
        });
        
        video.addEventListener('error', function() {
            updateMainStatus('❌ 移动端视频加载失败', '#f44336');
            updateVideoStatus('加载失败');
        });
        
        video.addEventListener('canplay', function() {
            updateVideoStatus('可播放');
        });
        
        // 检测触摸事件
        video.addEventListener('touchstart', function() {
            controlsStatus.textContent = '触摸正常';
        });
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            updateMainStatus('📱 移动端视频播放器初始化完成', '#2196f3');
        });
    </script>
</body>
</html>
