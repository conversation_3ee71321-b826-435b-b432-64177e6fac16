<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频Poster测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .video-container {
            width: 270px;
            height: 380px;
            margin: 20px auto;
            border: 2px solid #ffffff;
            border-radius: 12px;
            overflow: hidden;
            background: #000;
        }
        video {
            width: 270px !important;
            height: 380px !important;
            object-fit: cover;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .info {
            text-align: center;
            margin: 20px 0;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频Poster功能测试</h1>
        
        <div class="video-container">
            <video id="test-video" controls preload="metadata" playsinline>
                <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/sample-video.mp4" type="video/mp4">
                您的浏览器不支持HTML5视频播放。
            </video>
        </div>
        
        <div class="info">
            <p>测试视频播放器的poster功能</p>
            <p>应该显示视频的首帧而不是灰色背景</p>
        </div>
        
        <div class="test-buttons">
            <button onclick="generatePoster()">生成Poster</button>
            <button onclick="clearPoster()">清除Poster</button>
            <button onclick="testVideo()">测试播放</button>
        </div>
        
        <div id="status"></div>
    </div>

    <script>
        const video = document.getElementById('test-video');
        const status = document.getElementById('status');
        
        function updateStatus(message) {
            status.innerHTML = `<p style="color: #007bff; text-align: center;">${message}</p>`;
        }
        
        function generatePoster() {
            updateStatus('正在生成poster...');
            
            // 等待视频加载
            if (video.readyState >= 1) {
                createPoster();
            } else {
                video.addEventListener('loadeddata', createPoster, { once: true });
            }
        }
        
        function createPoster() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = 270;
                canvas.height = 380;
                
                // 设置视频到第1秒
                video.currentTime = 1;
                
                video.addEventListener('seeked', function() {
                    try {
                        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                        const posterUrl = canvas.toDataURL('image/jpeg', 0.8);
                        video.poster = posterUrl;
                        updateStatus('Poster生成成功！');
                    } catch (error) {
                        updateStatus('生成poster失败: ' + error.message);
                    }
                }, { once: true });
                
            } catch (error) {
                updateStatus('生成poster失败: ' + error.message);
            }
        }
        
        function clearPoster() {
            video.poster = '';
            updateStatus('Poster已清除');
        }
        
        function testVideo() {
            video.play().then(() => {
                updateStatus('视频播放成功');
            }).catch(error => {
                updateStatus('播放失败: ' + error.message);
            });
        }
        
        // 页面加载时自动尝试生成poster
        window.addEventListener('load', function() {
            updateStatus('页面加载完成，准备生成poster...');
            setTimeout(generatePoster, 1000);
        });
        
        // 视频事件监听
        video.addEventListener('loadeddata', function() {
            updateStatus('视频数据加载完成');
        });
        
        video.addEventListener('error', function() {
            updateStatus('视频加载失败');
        });
    </script>
</body>
</html>
